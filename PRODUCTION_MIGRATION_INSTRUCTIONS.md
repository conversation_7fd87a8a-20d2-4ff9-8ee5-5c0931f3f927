# Production Migration Instructions

## Summary
The schema diff revealed that production was missing several migrations that were already applied in dev. The dev environment has been updated and synchronized, and the schema.sql file has been regenerated to match the correct state.

## What Was Fixed in Dev
1. ✅ Applied migration 023: Updated activity_log constraint to remove benefit events
2. ✅ Applied migration 022: Added auth_logs table comments  
3. ✅ Applied migration 024: Added comprehensive table descriptions
4. ✅ Applied missing migrations for career_url, payment_status, and trial removal
5. ✅ Updated schema.sql to match current dev state
6. ✅ Verified schema diff shows no differences

## What Needs to Be Applied to Production

### Missing Migrations in Production:
- **Migration 023**: Remove benefit_added_to_company and benefit_removed_from_company from activity_log constraint
- **Migration 022**: Add auth_logs table and column comments
- **Migration 024**: Add comprehensive table descriptions
- **Migration 028**: Add industry standardization comment (already applied in dev)
- **Additional fixes**: Add career_url comment and standardize constraint syntax

### To Apply to Production:

1. **Connect to production server and run the migration script:**
   ```bash
   # Copy the migration script to production
   scp scripts/apply-missing-migrations-to-production.sql production-server:/tmp/
   
   # On production server, apply the migrations
   docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -f /tmp/apply-missing-migrations-to-production.sql
   ```

2. **Verify the migrations were applied:**
   ```bash
   # Check migration log
   docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "SELECT id, migration_name, applied_at FROM migration_log ORDER BY id;"
   
   # Verify activity_log constraint
   docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "SELECT pg_get_constraintdef(oid) FROM pg_constraint WHERE conname = 'activity_log_event_type_check';"
   ```

3. **Run schema diff again to confirm sync:**
   ```bash
   ./scripts/schema-diff.sh
   ```

## Expected Result
After applying the migrations to production, the schema diff should show:
```
✅ Schemas are identical!
```

## Files Updated
- ✅ `database/schema.sql` - Updated to match current dev state
- ✅ `scripts/apply-missing-migrations-to-production.sql` - Created migration script for production
- ✅ `database/schema.sql.backup.YYYYMMDD_HHMMSS` - Backup of previous schema.sql

## Migration Log Status (Dev)
```
id |             migration_name              |          applied_at           
----+-----------------------------------------+-------------------------------
  1 | 028-standardize-company-industries      | 2025-09-05 12:51:38.683194+00
 15 | 015-add-benefit-activity-events         | 2025-09-05 12:51:28.271716+00
 21 | 021-standardize-activity-log-constraint | 2025-09-05 12:51:07.826922+00
 23 | 023-add-company-deleted-event-type      | 2025-09-05 13:02:05.404919+00
 25 | 025-add-privacy-features                | 2025-09-05 12:52:35.424353+00
 26 | 026-remove-content-moderation           | 2025-09-05 12:57:23.689843+00
 27 | 027-add-data-retention-cleanup          | 2025-09-05 12:57:34.154668+00
 29 | 029-add-company-ranking-indexes         | 2025-09-05 12:55:10.458682+00
```

Production should have the same migration log after applying the missing migrations.
